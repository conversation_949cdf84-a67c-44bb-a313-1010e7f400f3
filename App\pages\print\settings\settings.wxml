<view class="container">
  <custom-nav title="打印设置" color="#333" showBack="{{true}}"></custom-nav>
  
  <!-- 文件信息区域 -->
  <view class="file-info">
    <view class="file-icon">
      <image src="{{fileIcon}}" mode="aspectFit"></image>
    </view>
    <view class="file-detail">
      <view class="file-name">{{fileName}}</view>
      <text class="preview-btn" bindtap="previewFile">预览</text>
    </view>
    <text class="close-btn">×</text>
  </view>
  
  <!-- 文件参数区域 -->
  <view class="file-preview">
    <view class="file-params">
      <view class="file-info-line">文件页数：{{totalPages}}页</view>
      <view class="file-spec-line">{{paperSize}}/{{colorType}}/{{printSide}}/{{paperType}}/{{binding}}/{{pageRange}}/{{direction}}/{{copies}}份</view>
    </view>
    <view class="file-price">
      <text class="price-symbol">¥</text>
      <text class="price-value">{{price}}</text>
      <text class="price-unit">元</text>
    </view>
  </view>
  
  <!-- 打印设置区域 -->
  <view class="settings-section">
    <!-- 加载状态 -->
    <view wx:if="{{isLoading}}" class="loading-container">
      <view class="loading-text">正在加载打印选项...</view>
    </view>

    <!-- 错误状态 -->
    <view wx:elif="{{loadError}}" class="error-container">
      <view class="error-text">加载打印选项失败</view>
      <button class="retry-btn" bindtap="retryLoadData">重试</button>
    </view>

    <!-- 设置选项（加载完成后显示） -->
    <view wx:else>
      <!-- 份数设置 -->
      <view class="setting-item">
        <view class="setting-label">份数</view>
        <view class="setting-control">
          <view class="quantity-control">
            <view class="quantity-btn minus" bindtap="decreaseCopies">-</view>
            <view class="quantity-value">{{copies}}</view>
            <view class="quantity-btn plus" bindtap="increaseCopies">+</view>
          </view>
        </view>
      </view>
    
    <!-- 打印范围 -->
    <view class="setting-item">
      <view class="setting-label">打印范围</view>
      <view class="setting-control">
        <view class="range-control">
          {{pageRange}}
          <view class="dropdown-icon">▼</view>
        </view>
      </view>
    </view>
    
    <!-- 纸张大小 -->
    <view class="setting-item">
      <view class="setting-label">大小</view>
      <view class="setting-control">
        <view class="option-group">
          <view wx:for="{{paperSizeOptions}}" wx:key="*this" 
                class="option-item {{paperSize === item ? 'active' : ''}}"
                bindtap="selectPaperSize" data-value="{{item}}">
            {{item}}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 单双面 -->
    <view class="setting-item">
      <view class="setting-label">单双面</view>
      <view class="setting-control">
        <view class="option-group">
          <view wx:for="{{printSideOptions}}" wx:key="*this" 
                class="option-item {{printSide === item ? 'active' : ''}}"
                bindtap="selectPrintSide" data-value="{{item}}">
            {{item}}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 彩色模式 -->
    <view class="setting-item">
      <view class="setting-label">色彩</view>
      <view class="setting-control">
        <view class="option-group">
          <view wx:for="{{colorTypeOptions}}" wx:key="*this" 
                class="option-item {{colorType === item ? 'active' : ''}}"
                bindtap="selectColorType" data-value="{{item}}">
            {{item}}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 彩色模式说明 -->
    <view class="setting-desc" wx:if="{{colorTypeDesc[colorType]}}">
      <text>{{colorTypeDesc[colorType]}}</text>
    </view>
    
    <!-- 纸张类型 -->
    <view class="setting-item">
      <view class="setting-label">纸张</view>
      <view class="setting-control">
        <view class="option-group">
          <view wx:for="{{paperTypeOptions}}" wx:key="*this" 
                class="option-item {{paperType === item ? 'active' : ''}}"
                bindtap="selectPaperType" data-value="{{item}}">
            {{item}}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 纸张类型说明 -->
    <view class="setting-desc" wx:if="{{paperTypeDesc[paperType]}}">
      <text>{{paperTypeDesc[paperType]}}</text>
    </view>
    
    <!-- 装订方式 -->
    <view class="setting-item">
      <view class="setting-label">装订</view>
      <view class="setting-control">
        <view class="option-group">
          <view wx:for="{{bindingOptions}}" wx:key="*this" 
                class="option-item {{binding === item ? 'active' : ''}}"
                bindtap="selectBinding" data-value="{{item}}">
            {{item}}
          </view>
        </view>
      </view>
    </view>
    </view> <!-- 关闭 wx:else -->
  </view>

  <!-- 底部操作区 -->
  <view class="bottom-action">
    <view class="total-price">
      合计: <text class="price-symbol">¥</text><text class="price-value">{{price}}</text>
    </view>
    <button class="confirm-btn" bindtap="submitPrint">确定</button>
  </view>
</view> 