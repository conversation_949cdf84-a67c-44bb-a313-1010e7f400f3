<view class="container">
  <custom-nav title="打印设置" color="#333" showBack="{{true}}"></custom-nav>
  
  <!-- 文件信息区域 -->
  <view class="file-info-section">
    <view class="file-header">
      <!-- 根据文件类型显示对应图标 -->
      <view class="file-icon-wrapper">
        <image wx:if="{{fileType === 'PDF'}}" class="file-icon" src="/images/icon/pdf_icon.png" mode="aspectFit"></image>
        <image wx:elif="{{fileType === 'DOC' || fileType === 'DOCX'}}" class="file-icon" src="/images/icon/word_icon.png" mode="aspectFit"></image>
        <image wx:elif="{{fileType === 'XLS' || fileType === 'XLSX'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
        <image wx:elif="{{fileType === 'PPT' || fileType === 'PPTX'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
        <image wx:elif="{{fileType === 'TXT'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
        <image wx:elif="{{fileType === 'JPG' || fileType === 'JPEG' || fileType === 'PNG' || fileType === 'GIF'}}" class="file-icon" src="/images/icon/image_icon.png" mode="aspectFit"></image>
        <image wx:else class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
      </view>

      <view class="file-info">
        <view class="file-name">{{fileName}}</view>
        <view class="file-specs">{{fileSpecs}}/第{{pageStart}}-{{pageEnd}}页</view>
      </view>
    </view>
  </view>
  
  <!-- 打印设置区域 -->
  <view class="settings-section">
    <!-- 加载状态 -->
    <view wx:if="{{isLoading}}" class="loading-container">
      <view class="loading-text">正在加载打印选项...</view>
    </view>

    <!-- 错误状态 -->
    <view wx:elif="{{loadError}}" class="error-container">
      <view class="error-text">加载打印选项失败</view>
      <button class="retry-btn" bindtap="retryLoadData">重试</button>
    </view>

    <!-- 设置选项（加载完成后显示） -->
    <view wx:else>
      <!-- 份数设置 -->
      <view class="setting-item">
        <view class="setting-label">份数</view>
        <view class="setting-control">
          <view class="quantity-control">
            <view class="setting-quantity-btn minus" bindtap="decreaseCopies">-</view>
            <view class="quantity-value">{{copies}}</view>
            <view class="setting-quantity-btn plus" bindtap="increaseCopies">+</view>
          </view>
        </view>
      </view>
    
    <!-- 打印范围 -->
    <view class="setting-item">
      <view class="setting-label">打印范围</view>
      <view class="setting-control">
        <view class="range-control">
          {{pageRange}}
          <view class="dropdown-icon">▼</view>
        </view>
      </view>
    </view>
    
    <!-- 纸张大小 -->
    <view class="setting-item">
      <view class="setting-label">大小</view>
      <view class="setting-control">
        <view class="option-group">
          <view wx:for="{{paperSizeOptions}}" wx:key="*this"
                class="option-item {{paperSize === item ? 'active' : ''}}"
                bindtap="selectPaperSize" data-value="{{item}}">
            {{item}}
          </view>
        </view>
      </view>
    </view>

    </view> <!-- 关闭 wx:else -->
  </view>

  <!-- 底部操作区 -->
  <view class="bottom-action">
    <view class="total-price">
      合计: <text class="price-symbol">¥</text><text class="price-value">{{price}}</text>
    </view>
    <button class="confirm-btn" bindtap="submitPrint">确定</button>
  </view>
  
</view> 