var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();

// 打印规格和属性数据（从API获取）
let PRINT_SPECIFICATIONS = []; // 从API获取的规格列表
let PRINT_ATTRIBUTES = []; // 从API获取的属性列表

Page({
  data: {
    fileName: '', // 文件名
    fileSize: 0, // 文件大小
    filePath: '', // 文件路径
    fileIcon: '/images/icon/icon_word.png', // 文件图标

    // 文件页数信息（与规格分离）
    totalPages: 6, // 文件总页数
    printStartPage: 1, // 打印起始页
    printEndPage: 6, // 打印结束页
    actualPrintPages: 6, // 实际打印页数
    pageRange: '1-6页', // 打印范围显示
    copies: 1, // 打印份数

    // 打印规格设置（从API获取）
    paperSize: 'A4', // 纸张大小
    paperSizeOptions: [],
    printSide: '双面', // 单双面
    printSideOptions: [],
    direction: '竖版', // 打印方向
    directionOptions: [],
    colorType: '经济彩印', // 颜色模式
    colorTypeOptions: [],
    colorCoverage: '7.05%', // 彩色覆盖率
    paperType: '护眼纸', // 纸张类型
    paperTypeOptions: [],
    binding: '订书钉', // 装订方式
    bindingOptions: [],
    multiPage: '不缩印', // 多页合一
    multiPageOptions: [],

    // API数据
    printSpecs: [], // 从API获取的规格列表
    printAttrs: [], // 从API获取的属性列表

    // 匹配的规格和价格
    matchedSpecification: null, // 匹配的规格对象
    price: 0.68, // 总价格
    
    // 颜色模式描述
    colorTypeDesc: {
      '经济彩印': '喷墨打印，可以满足绝大多数的日常需求\n哑光效果更护眼，按文件色彩覆盖率计价\n比激光更加透字，色准稍差，文件不防水',
      '黑白': '黑白打印，经济实惠\n适合文字为主的文档，不需要彩色效果',
      '标准彩印': '标准彩色打印，色彩鲜艳\n适合对色彩要求较高的文档，图片清晰，色彩准确'
    },
    
    // 纸张类型描述
    paperTypeDesc: {
      '普通纸': '标准70g复印纸，适合日常打印使用',
      '高端纸': '80g高品质纸张，纸质更好，手感更佳',
      '护眼纸': '米白色80g护眼纸，眼睛不易疲劳，预防近视\n权威检测，符合国标GB40070近视防控要求'
    }
  },

  onLoad: function(options) {
    // 首先加载打印属性和规格数据
    this.loadPrintData().then(() => {
      if (options.fileName) {
        // 从上传页面传入的参数
        const fileName = decodeURIComponent(options.fileName);
        const fileIcon = this.getFileIcon(fileName);
        const filePath = options.filePath ? decodeURIComponent(options.filePath) : '';
        this.setData({
          fileName: fileName,
          fileSize: options.fileSize || 0,
          fileIcon: fileIcon,
          filePath: filePath
        });

        // 获取文件页数
        this.getFilePageCount(filePath);
      } else if (options.id) {
        // 从打印列表页面传入的ID
        // 模拟从服务器获取文件信息，实际项目中应该调用API
        const mockFile = {
          fileName: 'GPU目标检测.doc',
          fileIcon: '/images/icon/icon_word.png',
          totalPages: 6,
          printStartPage: 1,
          printEndPage: 6,
          actualPrintPages: 6,
          copies: 1,
          pageRange: '1-6页',
          paperSize: 'A4',
          printSide: '双面',
          direction: '竖版',
          colorType: '经济彩印',
          paperType: '护眼纸',
          binding: '订书钉',
          multiPage: '不缩印',
          price: 0.68
        };

        this.setData({
          fileName: mockFile.fileName,
          fileIcon: mockFile.fileIcon,
          totalPages: mockFile.totalPages,
          printStartPage: mockFile.printStartPage,
          printEndPage: mockFile.printEndPage,
          actualPrintPages: mockFile.actualPrintPages,
          copies: mockFile.copies,
          pageRange: mockFile.pageRange,
          paperSize: mockFile.paperSize,
          printSide: mockFile.printSide,
          direction: mockFile.direction,
          colorType: mockFile.colorType,
          paperType: mockFile.paperType,
          binding: mockFile.binding,
          multiPage: mockFile.multiPage,
          price: mockFile.price
        });
      }

      // 初始化价格计算
      this.calculatePrice();
    });
  },

  // 加载打印数据（属性和规格）
  loadPrintData: function() {
    return Promise.all([
      this.loadPrintAttributes(),
      this.loadPrintSpecs()
    ]);
  },

  // 加载打印属性
  loadPrintAttributes: function() {
    return new Promise((resolve, reject) => {
      sender.requestUrl({
        url: api.api_print_attr,
        method: 'GET'
      }, (data) => {
        console.log('打印属性数据:', data);

        // 处理属性数据，构建选项数组
        const attrs = data || [];
        PRINT_ATTRIBUTES = attrs;

        // 根据属性名称设置对应的选项
        attrs.forEach(attr => {
          const attrValues = attr.attrValues.map(item => item.attrValue);

          switch(attr.attrName) {
            case '纸张大小':
            case 'paperSize':
              this.setData({ paperSizeOptions: attrValues });
              if (attrValues.length > 0 && !this.data.paperSize) {
                this.setData({ paperSize: attrValues[0] });
              }
              break;
            case '单双面':
            case 'printSide':
              this.setData({ printSideOptions: attrValues });
              if (attrValues.length > 0 && !this.data.printSide) {
                this.setData({ printSide: attrValues[0] });
              }
              break;
            case '打印方向':
            case 'direction':
              this.setData({ directionOptions: attrValues });
              if (attrValues.length > 0 && !this.data.direction) {
                this.setData({ direction: attrValues[0] });
              }
              break;
            case '颜色模式':
            case 'colorType':
              this.setData({ colorTypeOptions: attrValues });
              if (attrValues.length > 0 && !this.data.colorType) {
                this.setData({ colorType: attrValues[0] });
              }
              break;
            case '纸张类型':
            case 'paperType':
              this.setData({ paperTypeOptions: attrValues });
              if (attrValues.length > 0 && !this.data.paperType) {
                this.setData({ paperType: attrValues[0] });
              }
              break;
            case '装订方式':
            case 'binding':
              this.setData({ bindingOptions: attrValues });
              if (attrValues.length > 0 && !this.data.binding) {
                this.setData({ binding: attrValues[0] });
              }
              break;
            case '多页合一':
            case 'multiPage':
              this.setData({ multiPageOptions: attrValues });
              if (attrValues.length > 0 && !this.data.multiPage) {
                this.setData({ multiPage: attrValues[0] });
              }
              break;
          }
        });

        this.setData({ printAttrs: attrs });
        resolve(attrs);
      }, (error) => {
        console.error('加载打印属性失败:', error);
        // 使用默认值
        this.setDefaultAttributes();
        resolve([]);
      });
    });
  },

  // 加载打印规格
  loadPrintSpecs: function() {
    return new Promise((resolve, reject) => {
      sender.requestUrl({
        url: api.api_print_specs,
        method: 'GET'
      }, (data) => {
        console.log('打印规格数据:', data);

        const specs = data || [];
        PRINT_SPECIFICATIONS = specs;
        this.setData({ printSpecs: specs });
        resolve(specs);
      }, (error) => {
        console.error('加载打印规格失败:', error);
        resolve([]);
      });
    });
  },

  // 设置默认属性（当API加载失败时使用）
  setDefaultAttributes: function() {
    this.setData({
      paperSizeOptions: ['A4', 'A3', 'B5'],
      printSideOptions: ['单面', '双面', '单双面混排'],
      directionOptions: ['竖版', '横版'],
      colorTypeOptions: ['经济彩印', '黑白', '标准彩印'],
      paperTypeOptions: ['普通纸', '高端纸', '护眼纸'],
      bindingOptions: ['不装订', '订书钉', '胶装', '骑马钉'],
      multiPageOptions: ['不缩印', '2页合1', '4页合1', '6页合1', '9页合1']
    });
  },

  // 获取文件页数
  getFilePageCount: function(filePath) {
    // 模拟API调用获取文件页数
    // 实际项目中应该调用后端API解析文件
    sender.requestUrl({
      url: api.api_file_page_count || '/api/file/pageCount', // 假设的API端点
      method: 'GET',
      params: {
        filePath: filePath
      }
    }, (res) => {
      if (res.data && res.data.code === 0) {
        const pageCount = res.data.pageCount || 6; // 默认6页
        this.setData({
          totalPages: pageCount,
          printEndPage: pageCount,
          actualPrintPages: pageCount,
          pageRange: `1-${pageCount}页`
        });
        this.calculatePrice(); // 重新计算价格
      }
    }, (error) => {
      console.log('获取文件页数失败，使用默认值');
      // 使用默认页数
      this.setData({
        totalPages: 6,
        printEndPage: 6,
        actualPrintPages: 6,
        pageRange: '1-6页'
      });
      this.calculatePrice();
    });
  },
  
  // 根据文件扩展名获取图标
  getFileIcon: function(fileName) {
    const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    const iconMap = {
      'doc': '/images/icon/icon_word.png',
      'docx': '/images/icon/icon_word.png',
      'pdf': '/images/icon/icon_pdf.png',
      'ppt': '/images/icon/icon_ppt.png',
      'pptx': '/images/icon/icon_ppt.png',
      'xls': '/images/icon/icon_excel.png',
      'xlsx': '/images/icon/icon_excel.png',
      'jpg': '/images/icon/icon_image.png',
      'jpeg': '/images/icon/icon_image.png',
      'png': '/images/icon/icon_image.png',
      'zip': '/images/icon/icon_zip.png',
      'rar': '/images/icon/icon_zip.png'
    };
    
    return iconMap[ext] || '/images/icon/icon_file.png';
  },
  
  // 减少份数
  decreaseCopies: function() {
    if (this.data.copies > 1) {
      this.setData({
        copies: this.data.copies - 1
      });
      this.calculatePrice();
    }
  },
  
  // 增加份数
  increaseCopies: function() {
    this.setData({
      copies: this.data.copies + 1
    });
    this.calculatePrice();
  },
  
  // 选择纸张大小
  selectPaperSize: function(e) {
    this.setData({
      paperSize: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择单双面
  selectPrintSide: function(e) {
    this.setData({
      printSide: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择颜色模式
  selectColorType: function(e) {
    this.setData({
      colorType: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择纸张类型
  selectPaperType: function(e) {
    this.setData({
      paperType: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择装订方式
  selectBinding: function(e) {
    this.setData({
      binding: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择多页合一
  selectMultiPage: function(e) {
    this.setData({
      multiPage: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 查找匹配的规格
  findMatchingSpecification: function() {
    const currentSettings = {
      paperSize: this.data.paperSize,
      colorType: this.data.colorType,
      printSide: this.data.printSide,
      paperType: this.data.paperType,
      binding: this.data.binding,
      multiPage: this.data.multiPage,
      direction: this.data.direction
    };

    // 构建规格字符串用于匹配
    const currentSpecsString = this.buildSpecsString(currentSettings);
    console.log('当前设置规格字符串:', currentSpecsString);

    // 在API返回的规格中查找匹配项
    let matchedSpec = PRINT_SPECIFICATIONS.find(spec => {
      // 解析规格字符串或直接比较
      if (spec.specs) {
        return this.compareSpecs(spec.specs, currentSpecsString);
      }
      return false;
    });

    // 如果没有找到匹配的规格，使用第一个作为默认
    if (!matchedSpec && PRINT_SPECIFICATIONS.length > 0) {
      matchedSpec = PRINT_SPECIFICATIONS[0];
      console.log('未找到匹配规格，使用默认规格:', matchedSpec);
    }

    // 如果还是没有规格数据，创建一个默认规格
    if (!matchedSpec) {
      matchedSpec = {
        id: 'default',
        specs: currentSpecsString,
        amount: 0.1, // 默认价格
        note: '默认规格'
      };
      console.log('创建默认规格:', matchedSpec);
    }

    return matchedSpec;
  },

  // 构建规格字符串
  buildSpecsString: function(settings) {
    // 根据当前设置构建规格字符串，格式可能类似："A4/黑白/单面/普通纸/不装订"
    return `${settings.paperSize}/${settings.colorType}/${settings.printSide}/${settings.paperType}/${settings.binding}`;
  },

  // 比较规格字符串
  compareSpecs: function(apiSpecs, currentSpecs) {
    // 这里可以实现更复杂的匹配逻辑
    // 简单的字符串匹配
    if (apiSpecs === currentSpecs) {
      return true;
    }

    // 可以实现部分匹配逻辑
    const apiParts = apiSpecs.split('/');
    const currentParts = currentSpecs.split('/');

    // 计算匹配度
    let matchCount = 0;
    const minLength = Math.min(apiParts.length, currentParts.length);

    for (let i = 0; i < minLength; i++) {
      if (apiParts[i] === currentParts[i]) {
        matchCount++;
      }
    }

    // 如果匹配度超过70%，认为是匹配的
    return (matchCount / minLength) >= 0.7;
  },

  // 计算价格
  calculatePrice: function() {
    // 查找匹配的规格
    const matchedSpec = this.findMatchingSpecification();

    if (!matchedSpec) {
      console.error('未找到匹配的规格');
      return;
    }

    // 使用API返回的amount作为单价
    const unitPrice = matchedSpec.amount || 0.1;

    // 计算总价格：单价 × 实际打印页数 × 份数
    let totalPrice = unitPrice * this.data.actualPrintPages * this.data.copies;

    // 更新数据
    this.setData({
      matchedSpecification: matchedSpec,
      price: totalPrice.toFixed(2)
    });

    console.log('匹配的规格:', matchedSpec);
    console.log('计算详情:', {
      unitPrice: unitPrice,
      actualPrintPages: this.data.actualPrintPages,
      copies: this.data.copies,
      totalPrice: totalPrice.toFixed(2),
      specs: matchedSpec.specs
    });
  },
  
  // 预览文件
  previewFile: function() {
    wx.showToast({
      title: '预览功能暂未开放',
      icon: 'none'
    });
  },
  
  // 确认提交打印订单
  submitPrint: function() {
    const matchedSpec = this.data.matchedSpecification;

    if (!matchedSpec) {
      wx.showToast({
        title: '请先选择打印规格',
        icon: 'none'
      });
      return;
    }

    // 构建打印文件数据
    const printFileData = {
      fileName: this.data.fileName,
      filePath: this.data.filePath,
      pageNum: this.data.printStartPage,
      pageSize: this.data.actualPrintPages,
      quantity: this.data.copies,
      skuId: matchedSpec.id
    };

    console.log('提交打印数据:', printFileData);

    // 调用上传打印文件接口
    sender.requestUrl({
      url: api.api_order_detail || '/users-api/v1/order/detail',
      method: 'POST',
      data: printFileData
    }, (res) => {
      console.log('上传打印文件成功:', res);
      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });

      // 跳转到打印列表页面
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/print/list/list'
        });
      }, 1500);
    }, (error) => {
      console.error('上传打印文件失败:', error);
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    });
  }
}) 