var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();



Page({
  data: {
    fileName: '', // 文件名
    fileSize: 0, // 文件大小
    filePath: '', // 文件路径
    fileIcon: '/images/icon/icon_word.png', // 文件图标

    // 文件页数信息（与规格分离）
    totalPages: 6, // 文件总页数
    printStartPage: 1, // 打印起始页
    printEndPage: 6, // 打印结束页
    actualPrintPages: 6, // 实际打印页数
    pageRange: '1-6页', // 打印范围显示
    copies: 1, // 打印份数

    // 打印规格设置（从API动态获取）
    paperSize: '', // 纸张大小
    paperSizeOptions: [],

    // 价格
    price: 0.00, // 总价格

    // 加载状态
    isLoading: true,
    loadError: false
  },

  onLoad: function(options) {
    // 首先加载打印属性和规格数据
    this.loadPrintData().then(() => {
      if (options.fileName) {
        // 从上传页面传入的参数
        const fileName = decodeURIComponent(options.fileName);
        const fileIcon = this.getFileIcon(fileName);
        const filePath = options.filePath ? decodeURIComponent(options.filePath) : '';
        this.setData({
          fileName: fileName,
          fileSize: options.fileSize || 0,
          fileIcon: fileIcon,
          filePath: filePath
        });

        // 获取文件页数
        this.getFilePageCount(filePath);
      } else if (options.id) {
        // 从打印列表页面传入的ID
        // 模拟从服务器获取文件信息，实际项目中应该调用API
        const mockFile = {
          fileName: 'GPU目标检测.doc',
          fileIcon: '/images/icon/icon_word.png',
          totalPages: 6,
          printStartPage: 1,
          printEndPage: 6,
          actualPrintPages: 6,
          copies: 1,
          pageRange: '1-6页',
          paperSize: 'A4',
          printSide: '双面',
          direction: '竖版',
          colorType: '经济彩印',
          paperType: '护眼纸',
          binding: '订书钉',
          multiPage: '不缩印',
          price: 0.68
        };

        this.setData({
          fileName: mockFile.fileName,
          fileIcon: mockFile.fileIcon,
          totalPages: mockFile.totalPages,
          printStartPage: mockFile.printStartPage,
          printEndPage: mockFile.printEndPage,
          actualPrintPages: mockFile.actualPrintPages,
          copies: mockFile.copies,
          pageRange: mockFile.pageRange,
          paperSize: mockFile.paperSize,
          printSide: mockFile.printSide,
          direction: mockFile.direction,
          colorType: mockFile.colorType,
          paperType: mockFile.paperType,
          binding: mockFile.binding,
          multiPage: mockFile.multiPage,
          price: mockFile.price
        });
      }

      // 初始化价格计算
      this.calculatePrice();
    }).catch((error) => {
      console.error('加载打印数据失败:', error);
      // 错误处理已在loadPrintAttributes中处理，这里不需要额外处理
    });
  },

  // 加载打印数据
  loadPrintData: function() {
    return this.loadPrintAttributes();
  },

  // 加载打印属性
  loadPrintAttributes: function() {
    return new Promise((resolve, reject) => {
      sender.requestUrl({
        url: api.api_print_attr,
        method: 'GET'
      }, (data) => {
        console.log('打印属性数据:', data);

        // 处理属性数据，只提取纸张大小选项
        const attrs = data || [];
        const updateData = { isLoading: false };

        // 查找纸张大小属性
        const paperSizeAttr = attrs.find(attr =>
          attr.attrName === '纸张大小' || attr.attrName === 'paperSize'
        );

        if (paperSizeAttr && paperSizeAttr.attrValues) {
          const paperSizeOptions = paperSizeAttr.attrValues.map(item => item.attrValue);
          updateData.paperSizeOptions = paperSizeOptions;

          // 设置默认值（如果当前没有值）
          if (paperSizeOptions.length > 0 && !this.data.paperSize) {
            updateData.paperSize = paperSizeOptions[0];
          }
        }

        this.setData(updateData);
        console.log('属性数据更新完成:', updateData);
        resolve(attrs);
      }, (error) => {
        console.error('加载打印属性失败:', error);
        // 设置加载失败状态
        this.setData({
          isLoading: false,
          loadError: true
        });
        // 显示错误提示
        wx.showToast({
          title: '加载打印选项失败，请重试',
          icon: 'none',
          duration: 3000
        });
        reject(error);
      });
    });
  },



  // 重试加载数据
  retryLoadData: function() {
    this.setData({
      isLoading: true,
      loadError: false
    });
    this.loadPrintData().catch(() => {
      // 错误处理已在loadPrintAttributes中处理
    });
  },

  // 获取文件页数
  getFilePageCount: function(filePath) {
    // 模拟API调用获取文件页数
    // 实际项目中应该调用后端API解析文件
    sender.requestUrl({
      url: api.api_file_page_count || '/api/file/pageCount', // 假设的API端点
      method: 'GET',
      params: {
        filePath: filePath
      }
    }, (res) => {
      if (res.data && res.data.code === 0) {
        const pageCount = res.data.pageCount || 6; // 默认6页
        this.setData({
          totalPages: pageCount,
          printEndPage: pageCount,
          actualPrintPages: pageCount,
          pageRange: `1-${pageCount}页`
        });
        this.calculatePrice(); // 重新计算价格
      }
    }, (error) => {
      console.log('获取文件页数失败，使用默认值');
      // 使用默认页数
      this.setData({
        totalPages: 6,
        printEndPage: 6,
        actualPrintPages: 6,
        pageRange: '1-6页'
      });
      this.calculatePrice();
    });
  },
  
  // 减少份数
  decreaseCopies: function() {
    if (this.data.copies > 1) {
      this.setData({
        copies: this.data.copies - 1
      });
      this.calculatePrice();
    }
  },
  
  // 增加份数
  increaseCopies: function() {
    this.setData({
      copies: this.data.copies + 1
    });
    this.calculatePrice();
  },

  // 计算价格
  calculatePrice: function() {
    // 简化价格计算：固定单价 × 页数 × 份数
    const unitPrice = 0.1; // 固定单价，实际项目中应该从API获取
    let totalPrice = unitPrice * this.data.actualPrintPages * this.data.copies;

    this.setData({
      price: totalPrice.toFixed(2)
    });

    console.log('价格计算:', {
      unitPrice: unitPrice,
      actualPrintPages: this.data.actualPrintPages,
      copies: this.data.copies,
      totalPrice: totalPrice.toFixed(2)
    });
  },
  
  
  // 确认提交打印订单
  submitPrint: function() {
    // 构建打印文件数据
    const printFileData = {
      fileName: this.data.fileName,
      filePath: this.data.filePath,
      pageNum: this.data.printStartPage,
      pageSize: this.data.actualPrintPages,
      quantity: this.data.copies,
      paperSize: this.data.paperSize
    };

    console.log('提交打印数据:', printFileData);

    // 调用上传打印文件接口
    sender.requestUrl({
      url: api.api_order_detail || '/users-api/v1/order/detail',
      method: 'POST',
      data: printFileData
    }, (res) => {
      console.log('上传打印文件成功:', res);
      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });

      // 跳转到打印列表页面
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/print/list/list'
        });
      }, 1500);
    }, (error) => {
      console.error('上传打印文件失败:', error);
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    });
  }
}) 