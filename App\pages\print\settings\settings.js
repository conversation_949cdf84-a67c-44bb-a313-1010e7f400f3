var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();



Page({
  data: {
    printFile:{
      "fileName": "测试文件.PDF",
      "type": "PDF",
      "pageNum": 1,
      "pageSize": 10,
      "quantity": 1,
      "unit_price": 1.68,
      "skuId": 1,
      "specs": "A4/喷墨/双面",
      "selected": false
    }
  },

  onLoad: function(options) {
   
  },

  // 加载打印属性
  loadPrintAttributes: function() {
    return new Promise((resolve, reject) => {
      sender.requestUrl({
        url: api.api_print_attr,
        method: 'GET'
      }, (data) => {
        console.log('打印属性数据:', data);

        // 处理属性数据，只提取纸张大小选项
        const attrs = data || [];
        const updateData = { isLoading: false };

        // 查找纸张大小属性
        const paperSizeAttr = attrs.find(attr =>
          attr.attrName === '纸张大小' || attr.attrName === 'paperSize'
        );

        if (paperSizeAttr && paperSizeAttr.attrValues) {
          const paperSizeOptions = paperSizeAttr.attrValues.map(item => item.attrValue);
          updateData.paperSizeOptions = paperSizeOptions;

          // 设置默认值（如果当前没有值）
          if (paperSizeOptions.length > 0 && !this.data.paperSize) {
            updateData.paperSize = paperSizeOptions[0];
          }
        }

        this.setData(updateData);
        console.log('属性数据更新完成:', updateData);
        resolve(attrs);
      }, (error) => {
        console.error('加载打印属性失败:', error);
        // 设置加载失败状态
        this.setData({
          isLoading: false,
          loadError: true
        });
        // 显示错误提示
        wx.showToast({
          title: '加载打印选项失败，请重试',
          icon: 'none',
          duration: 3000
        });
        reject(error);
      });
    });
  }
}) 