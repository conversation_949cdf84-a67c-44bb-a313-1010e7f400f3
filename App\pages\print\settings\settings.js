var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();

Page({
  data: {
    fileName: '', // 文件名
    fileSize: 0, // 文件大小
    filePath: '', // 文件路径
    fileIcon: '/images/icon/icon_word.png', // 文件图标
    
    // 打印设置
    copies: 1, // 份数
    pageRange: '1-6页', // 打印范围
    paperSize: 'A4', // 纸张大小
    paperSizeOptions: ['A4', 'A3', 'B5'],
    printSide: '双面', // 单双面
    printSideOptions: ['单面', '双面', '单双面混排'],
    direction: '竖版', // 打印方向
    directionOptions: ['竖版', '横版'],
    colorType: '经济彩印', // 颜色模式
    colorTypeOptions: ['经济彩印', '黑白', '标准彩印'],
    colorCoverage: '7.05%', // 彩色覆盖率
    paperType: '护眼纸', // 纸张类型
    paperTypeOptions: ['普通纸', '高端纸', '护眼纸'],
    binding: '订书钉', // 装订方式
    bindingOptions: ['不装订', '订书钉', '胶装', '骑马钉'],
    multiPage: '不缩印', // 多页合一
    multiPageOptions: ['不缩印', '2页合1', '4页合1', '6页合1', '9页合1'],
    
    price: 0.68, // 价格
    
    // 颜色模式描述
    colorTypeDesc: {
      '经济彩印': '喷墨打印，可以满足绝大多数的日常需求\n哑光效果更护眼，按文件色彩覆盖率计价\n比激光更加透字，色准稍差，文件不防水',
      '黑白': '黑白打印，经济实惠\n适合文字为主的文档，不需要彩色效果',
      '标准彩印': '标准彩色打印，色彩鲜艳\n适合对色彩要求较高的文档，图片清晰，色彩准确'
    },
    
    // 纸张类型描述
    paperTypeDesc: {
      '普通纸': '标准70g复印纸，适合日常打印使用',
      '高端纸': '80g高品质纸张，纸质更好，手感更佳',
      '护眼纸': '米白色80g护眼纸，眼睛不易疲劳，预防近视\n权威检测，符合国标GB40070近视防控要求'
    }
  },

  onLoad: function(options) {
    if (options.fileName) {
      // 从上传页面传入的参数
      const fileName = decodeURIComponent(options.fileName);
      const fileIcon = this.getFileIcon(fileName);
      const filePath = options.filePath ? decodeURIComponent(options.filePath) : '';
      this.setData({
        fileName: fileName,
        fileSize: options.fileSize || 0,
        fileIcon: fileIcon,
        filePath: filePath
      });
    } else if (options.id) {
      // 从打印列表页面传入的ID
      // 模拟从服务器获取文件信息，实际项目中应该调用API
      const mockFile = {
        fileName: 'GPU目标检测.doc',
        fileIcon: '/images/icon/icon_word.png',
        copies: 1,
        pageRange: '1-6页',
        paperSize: 'A4',
        printSide: '双面',
        direction: '竖版',
        colorType: '经济彩印',
        paperType: '护眼纸',
        binding: '订书钉',
        multiPage: '不缩印',
        price: 0.68
      };
      
      this.setData({
        fileName: mockFile.fileName,
        fileIcon: mockFile.fileIcon,
        copies: mockFile.copies,
        pageRange: mockFile.pageRange,
        paperSize: mockFile.paperSize,
        printSide: mockFile.printSide,
        direction: mockFile.direction,
        colorType: mockFile.colorType,
        paperType: mockFile.paperType,
        binding: mockFile.binding,
        multiPage: mockFile.multiPage,
        price: mockFile.price
      });
    }
  },
  
  // 根据文件扩展名获取图标
  getFileIcon: function(fileName) {
    const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    const iconMap = {
      'doc': '/images/icon/icon_word.png',
      'docx': '/images/icon/icon_word.png',
      'pdf': '/images/icon/icon_pdf.png',
      'ppt': '/images/icon/icon_ppt.png',
      'pptx': '/images/icon/icon_ppt.png',
      'xls': '/images/icon/icon_excel.png',
      'xlsx': '/images/icon/icon_excel.png',
      'jpg': '/images/icon/icon_image.png',
      'jpeg': '/images/icon/icon_image.png',
      'png': '/images/icon/icon_image.png',
      'zip': '/images/icon/icon_zip.png',
      'rar': '/images/icon/icon_zip.png'
    };
    
    return iconMap[ext] || '/images/icon/icon_file.png';
  },
  
  // 减少份数
  decreaseCopies: function() {
    if (this.data.copies > 1) {
      this.setData({
        copies: this.data.copies - 1
      });
      this.calculatePrice();
    }
  },
  
  // 增加份数
  increaseCopies: function() {
    this.setData({
      copies: this.data.copies + 1
    });
    this.calculatePrice();
  },
  
  // 选择纸张大小
  selectPaperSize: function(e) {
    this.setData({
      paperSize: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择单双面
  selectPrintSide: function(e) {
    this.setData({
      printSide: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择颜色模式
  selectColorType: function(e) {
    this.setData({
      colorType: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择纸张类型
  selectPaperType: function(e) {
    this.setData({
      paperType: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择装订方式
  selectBinding: function(e) {
    this.setData({
      binding: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择多页合一
  selectMultiPage: function(e) {
    this.setData({
      multiPage: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 计算价格
  calculatePrice: function() {
    // 模拟计算价格，实际项目中需要根据选项计算
    let basePrice = 0.1; // 基础单价
    
    // 根据纸张大小调整价格
    if (this.data.paperSize === 'A3') {
      basePrice *= 2;
    } else if (this.data.paperSize === 'B5') {
      basePrice *= 0.8;
    }
    
    // 根据单双面调整价格
    if (this.data.printSide === '双面') {
      basePrice *= 1.8;
    } else if (this.data.printSide === '单双面混排') {
      basePrice *= 1.9;
    }
    
    // 根据颜色模式调整价格
    if (this.data.colorType === '经济彩印') {
      basePrice *= 3;
    } else if (this.data.colorType === '标准彩印') {
      basePrice *= 5;
    }
    
    // 根据纸张类型调整价格
    if (this.data.paperType === '高端纸') {
      basePrice *= 1.2;
    } else if (this.data.paperType === '护眼纸') {
      basePrice *= 1.5;
    }
    
    // 根据装订方式调整价格
    if (this.data.binding !== '不装订') {
      basePrice += 0.5;
    }
    
    // 计算总价
    const totalPrice = (basePrice * 6 * this.data.copies).toFixed(2); // 假设6页
    
    this.setData({
      price: totalPrice
    });
  },
  
  // 预览文件
  previewFile: function() {
    wx.showToast({
      title: '预览功能暂未开放',
      icon: 'none'
    });
  },
  
  // 确认提交打印订单
  submitPrint: function() {
    wx.navigateTo({
      url: '/pages/print/list/list'
    });
  }
}) 