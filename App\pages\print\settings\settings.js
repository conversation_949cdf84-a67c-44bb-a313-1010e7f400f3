var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();

// 预定义打印规格配置
const PRINT_SPECIFICATIONS = [
  {
    id: 'spec_001',
    name: 'A4黑白单面普通纸',
    paperSize: 'A4',
    colorType: '黑白',
    printSide: '单面',
    paperType: '普通纸',
    binding: '不装订',
    multiPage: '不缩印',
    direction: '竖版',
    basePrice: 0.1
  },
  {
    id: 'spec_002',
    name: 'A4黑白双面普通纸',
    paperSize: 'A4',
    colorType: '黑白',
    printSide: '双面',
    paperType: '普通纸',
    binding: '不装订',
    multiPage: '不缩印',
    direction: '竖版',
    basePrice: 0.18
  },
  {
    id: 'spec_003',
    name: 'A4经济彩印单面普通纸',
    paperSize: 'A4',
    colorType: '经济彩印',
    printSide: '单面',
    paperType: '普通纸',
    binding: '不装订',
    multiPage: '不缩印',
    direction: '竖版',
    basePrice: 0.3
  },
  {
    id: 'spec_004',
    name: 'A4经济彩印双面护眼纸',
    paperSize: 'A4',
    colorType: '经济彩印',
    printSide: '双面',
    paperType: '护眼纸',
    binding: '订书钉',
    multiPage: '不缩印',
    direction: '竖版',
    basePrice: 0.81
  },
  {
    id: 'spec_005',
    name: 'A3黑白单面普通纸',
    paperSize: 'A3',
    colorType: '黑白',
    printSide: '单面',
    paperType: '普通纸',
    binding: '不装订',
    multiPage: '不缩印',
    direction: '竖版',
    basePrice: 0.2
  },
  {
    id: 'spec_006',
    name: 'A4标准彩印双面高端纸',
    paperSize: 'A4',
    colorType: '标准彩印',
    printSide: '双面',
    paperType: '高端纸',
    binding: '胶装',
    multiPage: '不缩印',
    direction: '竖版',
    basePrice: 1.08
  }
];

// 装订费用配置
const BINDING_FEES = {
  '不装订': 0,
  '订书钉': 0.5,
  '胶装': 2.0,
  '骑马钉': 1.0
};

Page({
  data: {
    fileName: '', // 文件名
    fileSize: 0, // 文件大小
    filePath: '', // 文件路径
    fileIcon: '/images/icon/icon_word.png', // 文件图标

    // 文件页数信息（与规格分离）
    totalPages: 6, // 文件总页数
    printStartPage: 1, // 打印起始页
    printEndPage: 6, // 打印结束页
    actualPrintPages: 6, // 实际打印页数
    pageRange: '1-6页', // 打印范围显示
    copies: 1, // 打印份数

    // 打印规格设置
    paperSize: 'A4', // 纸张大小
    paperSizeOptions: ['A4', 'A3', 'B5'],
    printSide: '双面', // 单双面
    printSideOptions: ['单面', '双面', '单双面混排'],
    direction: '竖版', // 打印方向
    directionOptions: ['竖版', '横版'],
    colorType: '经济彩印', // 颜色模式
    colorTypeOptions: ['经济彩印', '黑白', '标准彩印'],
    colorCoverage: '7.05%', // 彩色覆盖率
    paperType: '护眼纸', // 纸张类型
    paperTypeOptions: ['普通纸', '高端纸', '护眼纸'],
    binding: '订书钉', // 装订方式
    bindingOptions: ['不装订', '订书钉', '胶装', '骑马钉'],
    multiPage: '不缩印', // 多页合一
    multiPageOptions: ['不缩印', '2页合1', '4页合1', '6页合1', '9页合1'],

    // 匹配的规格和价格
    matchedSpecification: null, // 匹配的规格对象
    price: 0.68, // 总价格
    
    // 颜色模式描述
    colorTypeDesc: {
      '经济彩印': '喷墨打印，可以满足绝大多数的日常需求\n哑光效果更护眼，按文件色彩覆盖率计价\n比激光更加透字，色准稍差，文件不防水',
      '黑白': '黑白打印，经济实惠\n适合文字为主的文档，不需要彩色效果',
      '标准彩印': '标准彩色打印，色彩鲜艳\n适合对色彩要求较高的文档，图片清晰，色彩准确'
    },
    
    // 纸张类型描述
    paperTypeDesc: {
      '普通纸': '标准70g复印纸，适合日常打印使用',
      '高端纸': '80g高品质纸张，纸质更好，手感更佳',
      '护眼纸': '米白色80g护眼纸，眼睛不易疲劳，预防近视\n权威检测，符合国标GB40070近视防控要求'
    }
  },

  onLoad: function(options) {
    if (options.fileName) {
      // 从上传页面传入的参数
      const fileName = decodeURIComponent(options.fileName);
      const fileIcon = this.getFileIcon(fileName);
      const filePath = options.filePath ? decodeURIComponent(options.filePath) : '';
      this.setData({
        fileName: fileName,
        fileSize: options.fileSize || 0,
        fileIcon: fileIcon,
        filePath: filePath
      });

      // 获取文件页数
      this.getFilePageCount(filePath);
    } else if (options.id) {
      // 从打印列表页面传入的ID
      // 模拟从服务器获取文件信息，实际项目中应该调用API
      const mockFile = {
        fileName: 'GPU目标检测.doc',
        fileIcon: '/images/icon/icon_word.png',
        totalPages: 6,
        printStartPage: 1,
        printEndPage: 6,
        actualPrintPages: 6,
        copies: 1,
        pageRange: '1-6页',
        paperSize: 'A4',
        printSide: '双面',
        direction: '竖版',
        colorType: '经济彩印',
        paperType: '护眼纸',
        binding: '订书钉',
        multiPage: '不缩印',
        price: 0.68
      };

      this.setData({
        fileName: mockFile.fileName,
        fileIcon: mockFile.fileIcon,
        totalPages: mockFile.totalPages,
        printStartPage: mockFile.printStartPage,
        printEndPage: mockFile.printEndPage,
        actualPrintPages: mockFile.actualPrintPages,
        copies: mockFile.copies,
        pageRange: mockFile.pageRange,
        paperSize: mockFile.paperSize,
        printSide: mockFile.printSide,
        direction: mockFile.direction,
        colorType: mockFile.colorType,
        paperType: mockFile.paperType,
        binding: mockFile.binding,
        multiPage: mockFile.multiPage,
        price: mockFile.price
      });
    }

    // 初始化价格计算
    this.calculatePrice();
  },

  // 获取文件页数
  getFilePageCount: function(filePath) {
    // 模拟API调用获取文件页数
    // 实际项目中应该调用后端API解析文件
    sender.requestUrl({
      url: api.api_file_page_count || '/api/file/pageCount', // 假设的API端点
      method: 'GET',
      params: {
        filePath: filePath
      }
    }, (res) => {
      if (res.data && res.data.code === 0) {
        const pageCount = res.data.pageCount || 6; // 默认6页
        this.setData({
          totalPages: pageCount,
          printEndPage: pageCount,
          actualPrintPages: pageCount,
          pageRange: `1-${pageCount}页`
        });
        this.calculatePrice(); // 重新计算价格
      }
    }, (error) => {
      console.log('获取文件页数失败，使用默认值');
      // 使用默认页数
      this.setData({
        totalPages: 6,
        printEndPage: 6,
        actualPrintPages: 6,
        pageRange: '1-6页'
      });
      this.calculatePrice();
    });
  },
  
  // 根据文件扩展名获取图标
  getFileIcon: function(fileName) {
    const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    const iconMap = {
      'doc': '/images/icon/icon_word.png',
      'docx': '/images/icon/icon_word.png',
      'pdf': '/images/icon/icon_pdf.png',
      'ppt': '/images/icon/icon_ppt.png',
      'pptx': '/images/icon/icon_ppt.png',
      'xls': '/images/icon/icon_excel.png',
      'xlsx': '/images/icon/icon_excel.png',
      'jpg': '/images/icon/icon_image.png',
      'jpeg': '/images/icon/icon_image.png',
      'png': '/images/icon/icon_image.png',
      'zip': '/images/icon/icon_zip.png',
      'rar': '/images/icon/icon_zip.png'
    };
    
    return iconMap[ext] || '/images/icon/icon_file.png';
  },
  
  // 减少份数
  decreaseCopies: function() {
    if (this.data.copies > 1) {
      this.setData({
        copies: this.data.copies - 1
      });
      this.calculatePrice();
    }
  },
  
  // 增加份数
  increaseCopies: function() {
    this.setData({
      copies: this.data.copies + 1
    });
    this.calculatePrice();
  },
  
  // 选择纸张大小
  selectPaperSize: function(e) {
    this.setData({
      paperSize: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择单双面
  selectPrintSide: function(e) {
    this.setData({
      printSide: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择颜色模式
  selectColorType: function(e) {
    this.setData({
      colorType: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择纸张类型
  selectPaperType: function(e) {
    this.setData({
      paperType: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择装订方式
  selectBinding: function(e) {
    this.setData({
      binding: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 选择多页合一
  selectMultiPage: function(e) {
    this.setData({
      multiPage: e.currentTarget.dataset.value
    });
    this.calculatePrice();
  },
  
  // 查找匹配的规格
  findMatchingSpecification: function() {
    const currentSettings = {
      paperSize: this.data.paperSize,
      colorType: this.data.colorType,
      printSide: this.data.printSide,
      paperType: this.data.paperType,
      binding: this.data.binding,
      multiPage: this.data.multiPage,
      direction: this.data.direction
    };

    // 寻找完全匹配的规格
    let matchedSpec = PRINT_SPECIFICATIONS.find(spec => {
      return spec.paperSize === currentSettings.paperSize &&
             spec.colorType === currentSettings.colorType &&
             spec.printSide === currentSettings.printSide &&
             spec.paperType === currentSettings.paperType &&
             spec.binding === currentSettings.binding &&
             spec.multiPage === currentSettings.multiPage &&
             spec.direction === currentSettings.direction;
    });

    // 如果没有完全匹配，寻找最接近的规格
    if (!matchedSpec) {
      // 按优先级匹配：纸张大小 > 颜色类型 > 单双面 > 纸张类型
      matchedSpec = PRINT_SPECIFICATIONS.find(spec => {
        return spec.paperSize === currentSettings.paperSize &&
               spec.colorType === currentSettings.colorType &&
               spec.printSide === currentSettings.printSide;
      });
    }

    // 如果还是没有匹配，使用默认规格
    if (!matchedSpec) {
      matchedSpec = PRINT_SPECIFICATIONS[0]; // 使用第一个作为默认规格
    }

    return matchedSpec;
  },

  // 计算价格
  calculatePrice: function() {
    // 查找匹配的规格
    const matchedSpec = this.findMatchingSpecification();

    // 计算基础价格：规格基础价格 × 实际打印页数 × 份数
    let totalPrice = matchedSpec.basePrice * this.data.actualPrintPages * this.data.copies;

    // 添加装订费用（每份）
    const bindingFee = BINDING_FEES[this.data.binding] || 0;
    totalPrice += bindingFee * this.data.copies;

    // 更新数据
    this.setData({
      matchedSpecification: matchedSpec,
      price: totalPrice.toFixed(2)
    });

    console.log('匹配的规格:', matchedSpec);
    console.log('计算详情:', {
      basePrice: matchedSpec.basePrice,
      actualPrintPages: this.data.actualPrintPages,
      copies: this.data.copies,
      bindingFee: bindingFee,
      totalPrice: totalPrice.toFixed(2)
    });
  },
  
  // 预览文件
  previewFile: function() {
    wx.showToast({
      title: '预览功能暂未开放',
      icon: 'none'
    });
  },
  
  // 确认提交打印订单
  submitPrint: function() {
    wx.navigateTo({
      url: '/pages/print/list/list'
    });
  }
}) 