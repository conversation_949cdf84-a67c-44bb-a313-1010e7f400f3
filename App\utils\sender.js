var api = require('./api.js')

/**
 * 网络请求
 * @param request           {object}        请求对象
 * @param request.url       {string}        请求url
 * @param request.method    {string}        请求方法：OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
 * @param request.data      {object}        请求体数据
 * @param request.params    {object}        URL参数
 * @param successCallback   {function}      成功回调函数
 */
function requestUrl(request, successCallback) {
  // 处理请求参数
  let finalUrl = request.url;
  
  // 处理URL参数拼接
  if (request.params && Object.keys(request.params).length > 0) {
    finalUrl += '?';
    
    // 将params对象转换为URL参数字符串
    const paramsArray = [];
    for (const key in request.params) {
      if (request.params.hasOwnProperty(key)) {
        // 小程序会自动处理编码，无需手动编码
        paramsArray.push(key + '=' + request.params[key]);
      }
    }
    
    // 拼接参数到URL
    finalUrl += paramsArray.join('&');
  }

  // 尝试获取token
  let token = '';
  try {
    token = wx.getStorageSync('AccessToken').access_token;
  } catch (e) {
    console.log('获取Token失败');
  }

  wx.request({
    url: finalUrl,
    data: request.data || {},
    method: request.method || 'GET', // 默认为GET请求
    header: {
      'Content-Type': 'application/json',
      'Authorization': token ? token : ''
    },
    success: function (res) {
      if (res.statusCode == 200) { //网络请求正常
        if (res.data.code == 0) {
          successCallback(res.data.data);
        } else {
          wx.showToast({
            title: res.data.errMessage,
            icon: 'none'
          })
        }
      }
    },
    fail: function () {
      wx.showModal({
        title: '提示',
        content: '无法连接服务器',
        showCancel: false,
      })
    }
  });
}

module.exports = {
  requestUrl: requestUrl
}