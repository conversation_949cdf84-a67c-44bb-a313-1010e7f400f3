.container {
  padding-bottom: 260rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 打印文件列表 */
.print-list {
  margin-top: 20rpx;
}

.print-item {
  background-color: #fff;
  margin-bottom: 20rpx;
  display: flex;
  padding: 20rpx 30rpx;
}

.select-box {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.select-circle {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
}

.select-circle.selected {
  border-color: #ff6633;
}

.select-inner {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #ff6633;
}

.file-info {
  flex: 1;
}

.file-top {
  display: flex;
  align-items: center;
  position: relative;
}

.file-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.file-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 500rpx;
}

.delete-btn {
  font-size: 40rpx;
  color: #999;
  position: absolute;
  right: 0;
  top: 0;
}

.file-params {
  font-size: 24rpx;
  color: #999;
  margin: 15rpx 0;
}

/* 快速设置区域 */
.quick-settings {
  margin: 15rpx 0;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border: 1rpx solid #e5e5e5;
}

.quick-setting-item {
  margin-bottom: 15rpx;
}

.quick-setting-item:last-child {
  margin-bottom: 0;
}

.quick-setting-item .setting-label {
  font-size: 26rpx;
  color: #333;
  margin-right: 15rpx;
  min-width: 80rpx;
  display: inline-block;
}

.quick-setting-item .option-group {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.quick-setting-item .option-item {
  padding: 8rpx 16rpx;
  background-color: #fff;
  border: 1rpx solid #ddd;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.quick-setting-item .option-item.active {
  background-color: #ff6633;
  border-color: #ff6633;
  color: #fff;
}

.quick-setting-item .option-item:hover {
  background-color: #f0f0f0;
}

.quick-setting-item .option-item.active:hover {
  background-color: #e55a2b;
}

.file-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-price {
  display: flex;
  align-items: baseline;
}

.price-symbol, .price-unit {
  color: #ff6633;
  font-size: 24rpx;
}

.price-value {
  color: #ff6633;
  font-size: 32rpx;
  font-weight: bold;
}

.action-btns {
  display: flex;
}

.preview-btn, .settings-btn, .quick-btn {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
}

.preview-btn {
  color: #666;
  background-color: #f5f5f5;
}

.quick-btn {
  color: #007aff;
  border: 1px solid #007aff;
  background-color: #fff;
}

.settings-btn {
  color: #ff6633;
  border: 1px solid #ff6633;
}

.empty-tip {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 60rpx 0;
}

/* 预览提示 */
.preview-tip {
  background-color: #fff;
  padding: 30rpx;
  margin: 20rpx 0;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

/* 底部占位 */
.bottom-placeholder {
  height: 280rpx; /* 保证有足够的空间不被底部遮挡 */
}

/* 底部固定操作区 */
.bottom-fixed-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 100;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
}

/* 功能标签区 */
.function-tabs {
  display: flex;
  justify-content: space-around;
  height: 90rpx;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  padding: 0 10rpx;
}

/* 结算栏 */
.checkout-bar {
  display: flex;
  height: 100rpx;
  align-items: center;
  padding: 0 20rpx;
  position: relative;
}

/* 左侧选择 */
.left-selection {
  display: flex;
  align-items: center;
}

.select-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

/* 凑单免邮胶囊 */
.shipping-capsule {
  position: fixed;
  left: 85rpx;
  bottom: 0;
  width: 60rpx;
  height: 140rpx;
  border: 2rpx solid #ff6633;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  border-bottom: none;
  padding: 5rpx 0;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 110;
}

.shipping-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 5rpx;
}

.shipping-text text {
  color: #ff6633;
  font-size: 24rpx;
  line-height: 28rpx;
}

/* 右侧结算 */
.right-checkout {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.order-summary {
  font-size: 24rpx;
  color: #333;
  margin-right: 20rpx;
  display: flex;
  align-items: baseline;
}

.price-symbol {
  color: #ff6633;
  font-size: 24rpx;
}

.price-value {
  color: #ff6633;
  font-size: 32rpx;
  font-weight: bold;
}

/* 结算按钮 */
.checkout-btn {
  width: 200rpx;
  height: 100rpx;
  background-color: #ff6633;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0;
}

.checkout-btn:active {
  background-color: #e55a2b;
}

/* 悬浮添加按钮 */
.floating-add-btn {
  position: fixed;
  right: 30rpx;
  bottom: 280rpx; /* 在底部固定区域上方 */
  width: 100rpx;
  height: 100rpx;
  background-color: #ff6633;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(255, 102, 51, 0.3);
  z-index: 100;
  transition: all 0.3s ease;
}

.floating-add-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(255, 102, 51, 0.4);
}

.add-icon {
  font-size: 48rpx;
  color: #fff;
  font-weight: 300;
  line-height: 1;
}