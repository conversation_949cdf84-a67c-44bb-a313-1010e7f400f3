.container {
  padding-bottom: 260rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 打印文件列表 */
.print-list {
  margin-top: 20rpx;
}

.print-item {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 文件头部区域 */
.file-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.select-box {
  flex-shrink: 0;
}

.select-circle {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
}

.select-circle.selected {
  border-color: #ff6633;
  background: #ff6633;
}

.select-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #fff;
}

.file-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-icon {
  width: 50rpx;
  height: 50rpx;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  min-width: 0;
}

.file-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
}

.file-specs {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.delete-btn {
  width: 40rpx;
  height: 40rpx;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999;
  flex-shrink: 0;
}

/* 底部操作区域 */
.file-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  gap: 20rpx;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
  flex-shrink: 0;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff6633;
  font-weight: 500;
}

.price-value {
  font-size: 36rpx;
  color: #ff6633;
  font-weight: 600;
}

.price-unit {
  font-size: 24rpx;
  color: #ff6633;
  font-weight: 500;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 15rpx;
  flex: 1;
  justify-content: center;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
  background: #f8f8f8;
}

.quantity-input {
  width: 100rpx;
  height: 60rpx;
  text-align: center;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
}

.quantity-unit {
  font-size: 26rpx;
  color: #666;
}

.settings-btn {
  padding: 16rpx 32rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
  border: 1rpx solid #e0e0e0;
  flex-shrink: 0;
}



.empty-tip {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

/* 预览提示 */
.preview-tip {
  background-color: #fff;
  padding: 30rpx;
  margin: 20rpx 0;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

/* 底部占位 */
.bottom-placeholder {
  height: 280rpx; /* 保证有足够的空间不被底部遮挡 */
}

/* 底部固定操作区 */
.bottom-fixed-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 100;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
}

/* 功能标签区 */
.function-tabs {
  display: flex;
  justify-content: space-around;
  height: 90rpx;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  padding: 0 10rpx;
}

/* 结算栏 */
.checkout-bar {
  display: flex;
  height: 100rpx;
  align-items: center;
  padding: 0 20rpx;
  position: relative;
}

/* 左侧选择 */
.left-selection {
  display: flex;
  align-items: center;
}

.select-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

/* 凑单免邮胶囊 */
.shipping-capsule {
  position: fixed;
  left: 85rpx;
  bottom: 0;
  width: 60rpx;
  height: 140rpx;
  border: 2rpx solid #ff6633;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  border-bottom: none;
  padding: 5rpx 0;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 110;
}

.shipping-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 5rpx;
}

.shipping-text text {
  color: #ff6633;
  font-size: 24rpx;
  line-height: 28rpx;
}

/* 右侧结算 */
.right-checkout {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.order-summary {
  font-size: 24rpx;
  color: #333;
  margin-right: 20rpx;
  display: flex;
  align-items: baseline;
}

.price-symbol {
  color: #ff6633;
  font-size: 24rpx;
}

.price-value {
  color: #ff6633;
  font-size: 32rpx;
  font-weight: bold;
}

/* 结算按钮 */
.checkout-btn {
  width: 200rpx;
  height: 100rpx;
  background-color: #ff6633;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0;
}

.checkout-btn:active {
  background-color: #e55a2b;
}

/* 悬浮添加按钮 */
.floating-add-btn {
  position: fixed;
  right: 30rpx;
  bottom: 280rpx; /* 在底部固定区域上方 */
  width: 100rpx;
  height: 100rpx;
  background-color: #ff6633;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(255, 102, 51, 0.3);
  z-index: 100;
  transition: all 0.3s ease;
}

.floating-add-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(255, 102, 51, 0.4);
}

.add-icon {
  font-size: 48rpx;
  color: #fff;
  font-weight: 300;
  line-height: 1;
}