.container {
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 文件信息区域 */
.file-info {
  display: flex;
  background-color: #fff;
  padding: 20rpx 30rpx;
  align-items: center;
  position: relative;
  margin-top: 10rpx;
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.file-icon image {
  width: 100%;
  height: 100%;
}

.file-detail {
  flex: 1;
  overflow: hidden;
}

.file-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.preview-btn {
  font-size: 24rpx;
  color: #ff6633;
  padding: 4rpx 20rpx;
  border: 1px solid #ff6633;
  border-radius: 30rpx;
  display: inline-block;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  position: absolute;
  right: 30rpx;
  top: 20rpx;
  padding: 10rpx;
}

/* 文件预览参数 */
.file-preview {
  background-color: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.file-params {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  padding-right: 20rpx;
  line-height: 1.5;
}

.file-info-line {
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.file-spec-line {
  color: #666;
}

.file-price {
  display: flex;
  align-items: baseline;
  flex-shrink: 0;
}

.price-symbol, .price-unit {
  color: #ff6633;
  font-size: 28rpx;
}

.price-value {
  color: #ff6633;
  font-size: 36rpx;
  font-weight: bold;
}

/* 设置区域 */
.settings-section {
  margin-top: 20rpx;
  background-color: #fff;
}

/* 加载状态 */
.loading-container {
  padding: 80rpx 0;
  text-align: center;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  padding: 80rpx 0;
  text-align: center;
}

.error-text {
  color: #ff4757;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.retry-btn {
  background-color: #ff6b35;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
}

.setting-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-label {
  width: 150rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex-shrink: 0;
}

.setting-control {
  flex: 1;
  overflow: hidden;
}

/* 份数控制 */
.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #666;
}

.quantity-value {
  margin: 0 30rpx;
  font-size: 32rpx;
  color: #333;
}

/* 范围控制 */
.range-control, .dropdown-control {
  background-color: #ff6633;
  color: #fff;
  padding: 15rpx 30rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: fit-content;
}

.dropdown-icon {
  margin-left: 10rpx;
  font-size: 20rpx;
}

/* 选项组 */
.option-group {
  display: flex;
  flex-wrap: wrap;
}

.option-item {
  padding: 12rpx 20rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  background-color: #f5f5f5;
  color: #333;
  text-align: center;
}

.option-item.active {
  background-color: #ff6633;
  color: #fff;
}

/* 设置描述 */
.setting-desc {
  font-size: 24rpx;
  color: #999;
  padding: 0 30rpx 30rpx;
  line-height: 1.6;
  background-color: #fff;
}

/* 底部操作区 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  height: 100rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  z-index: 10;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.total-price {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 30rpx;
  font-size: 28rpx;
  color: #333;
}

.confirm-btn {
  width: 250rpx;
  height: 100%;
  background-color: #ff6633;
  color: #fff;
  border-radius: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
} 