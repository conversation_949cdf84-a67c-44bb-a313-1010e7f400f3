.container {
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 文件信息区域 */
.file-info-section {
  background-color: #fff;
  margin-top: 20rpx;
}

.file-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.file-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-icon {
  width: 50rpx;
  height: 50rpx;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  min-width: 0;
}

.file-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
}

.file-specs {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}



/* 设置区域 */
.settings-section {
  background-color: #fff;
}

/* 加载状态 */
.loading-container {
  padding: 80rpx 0;
  text-align: center;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  padding: 80rpx 0;
  text-align: center;
}

.error-text {
  color: #ff4757;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.retry-btn {
  background-color: #ff6b35;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
}

.setting-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-label {
  width: 150rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex-shrink: 0;
}

.setting-control {
  flex: 1;
  overflow: hidden;
}

/* 设置区域份数控制 */
.quantity-control {
  display: flex;
  align-items: center;
}

.setting-quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #666;
}

.quantity-value {
  margin: 0 30rpx;
  font-size: 32rpx;
  color: #333;
}

/* 范围控制 */
.range-control, .dropdown-control {
  background-color: #ff6633;
  color: #fff;
  padding: 15rpx 30rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: fit-content;
}

.dropdown-icon {
  margin-left: 10rpx;
  font-size: 20rpx;
}

/* 选项组 */
.option-group {
  display: flex;
  flex-wrap: wrap;
}

.option-item {
  padding: 12rpx 20rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  background-color: #f5f5f5;
  color: #333;
  text-align: center;
}

.option-item.active {
  background-color: #ff6633;
  color: #fff;
}

/* 设置描述 */
.setting-desc {
  font-size: 24rpx;
  color: #999;
  padding: 0 30rpx 30rpx;
  line-height: 1.6;
  background-color: #fff;
}

/* 底部操作区 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  height: 100rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  z-index: 10;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.total-price {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 30rpx;
  font-size: 28rpx;
  color: #333;
}

.confirm-btn {
  width: 250rpx;
  height: 100%;
  background-color: #ff6633;
  color: #fff;
  border-radius: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
} 