const API_HOST = 'http://127.0.0.1:8081'

/** 资源服务器地址 */
const CDN_IMAGE = API_HOST + '/common-api/v1/file/'

/** 登录URL */
const api_login = API_HOST + '/users-api/v1/auth/token'

/** 图文接口 */

const api_imagetext_list = API_HOST + '/users-api/v1/imagetext/page'

const api_file_upload = API_HOST + '/common-api/v1/file/upload'

const api_imagetext_detail = API_HOST + '/users-api/v1/imagetext'

const api_print_sku_list = API_HOST + '/users-api/v1/print/sku/list'

const api_print_preview = API_HOST + '/users-api/v1/print/preview'

const api_order_pre_order = API_HOST + '/users-api/v1/order/pre-order'

const api_address_page = API_HOST + '/users-api/v1/address/page'
const api_address_detail = API_HOST + '/users-api/v1/address'
const api_address_add = API_HOST + '/users-api/v1/address'
const api_address_update = API_HOST + '/users-api/v1/address'
const api_address_delete = API_HOST + '/users-api/v1/address'

module.exports = {
  CDN_IMAGE: CDN_IMAGE,
  API_HOST: API_HOST,
  api_login: api_login,
  api_print_preview: api_print_preview,
  api_order_pre_order: api_order_pre_order,
  api_file_upload: api_file_upload,
  api_print_sku_list: api_print_sku_list,
  api_imagetext_list: api_imagetext_list,
  api_imagetext_detail: api_imagetext_detail,
  api_address_page: api_address_page,
  api_address_detail: api_address_detail,
  api_address_add: api_address_add,
  api_address_update: api_address_update,
  api_address_delete: api_address_delete
}