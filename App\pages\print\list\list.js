var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();

Page({
  data: {
    printList: [],
    totalPrice: 0.68,
    selectedCount: 1,
    functions: [
      {
        name: '批量设置',
        icon: '/images/icon/icon_batch_set.png'
      },
      {
        name: '批量删除',
        icon: '/images/icon/icon_delete.png'
      }
    ],
    deliveryFree: true,
    freeShippingThreshold: 3.9,
    freeExpressThreshold: 9.9,
    // 打印属性数据（从API获取）
    printAttributes: [],
    printSpecs: [],
    // 动态选项数组（从API数据中提取）
    paperSizeOptions: [],
    printSideOptions: [],
    directionOptions: [],
    colorTypeOptions: [],
    paperTypeOptions: [],
    bindingOptions: [],
    multiPageOptions: []
  },

  onLoad: function(options) {
    console.log(options)

    // 首先加载打印属性数据
    this.loadPrintData().then(() => {
      let data = {
        fileName: options.fileName,
        filePath: options.filePath,
        quantity: 1,
        // 添加默认的打印设置（使用API数据的第一个值）
        printSettings: this.getDefaultPrintSettings(),
        // 格式化打印参数显示
        fileParams: this.formatPrintParams(this.getDefaultPrintSettings()),
        // 计算价格（这里先用固定值，实际应该根据设置计算）
        price: 0.68,
        // 添加文件图标
        fileIcon: this.getFileIcon(options.fileName || ''),
        // 添加选中状态
        selected: true,
        // 生成唯一ID
        id: Date.now(),
        // 快速设置展开状态
        showQuickSettings: false
      }
      this.setData({
        printList: [data]
      })
      this.calculateTotal();
    }).catch((error) => {
      console.error('加载打印数据失败:', error);
      // 即使加载失败也要显示文件，但不显示打印参数
      let data = {
        fileName: options.fileName,
        filePath: options.filePath,
        quantity: 1,
        fileParams: '加载打印选项失败',
        price: 0.00,
        fileIcon: this.getFileIcon(options.fileName || ''),
        selected: true,
        id: Date.now()
      }
      this.setData({
        printList: [data]
      })
    });
  },

  // 加载打印数据（属性和规格）
  loadPrintData: function() {
    return Promise.all([
      this.loadPrintAttributes(),
      this.loadPrintSpecs()
    ]);
  },

  // 加载打印属性
  loadPrintAttributes: function() {
    return new Promise((resolve, reject) => {
      sender.requestUrl({
        url: api.api_print_attr,
        method: 'GET'
      }, (data) => {
        console.log('打印属性数据:', data);

        // 处理API返回的数据，提取各种选项
        const updateData = {};
        const attrs = data || [];

        attrs.forEach(attr => {
          if (!attr.attrValues || attr.attrValues.length === 0) return;

          // 提取属性值数组
          const attrValues = attr.attrValues.map(item => item.attrValue);

          switch(attr.attrName) {
            case '纸张大小':
            case 'paperSize':
              updateData.paperSizeOptions = attrValues;
              break;
            case '单双面':
            case '打印面':
            case 'printSide':
              updateData.printSideOptions = attrValues;
              break;
            case '打印方向':
            case '方向':
            case 'direction':
              updateData.directionOptions = attrValues;
              break;
            case '颜色模式':
            case 'colorType':
              updateData.colorTypeOptions = attrValues;
              break;
            case '纸张类型':
            case 'paperType':
              updateData.paperTypeOptions = attrValues;
              break;
            case '装订方式':
            case 'binding':
              updateData.bindingOptions = attrValues;
              break;
            case '多页合一':
            case 'multiPage':
              updateData.multiPageOptions = attrValues;
              break;
          }
        });

        // 更新页面数据
        this.setData({
          printAttributes: attrs,
          ...updateData
        });

        resolve(attrs);
      }, (error) => {
        console.error('加载打印属性失败:', error);
        reject(error);
      });
    });
  },

  // 加载打印规格
  loadPrintSpecs: function() {
    return new Promise((resolve, reject) => {
      sender.requestUrl({
        url: api.api_print_specs,
        method: 'GET'
      }, (specs) => {
        console.log('打印规格数据:', specs);
        this.setData({
          printSpecs: specs
        });
        resolve(specs);
      }, (error) => {
        console.error('加载打印规格失败:', error);
        resolve([]); // 规格加载失败不影响属性显示
      });
    });
  },

  // 获取默认打印设置
  getDefaultPrintSettings: function() {
    const settings = {};

    // 使用动态获取的选项数组的第一个值作为默认值
    if (this.data.paperSizeOptions && this.data.paperSizeOptions.length > 0) {
      settings.paperSize = this.data.paperSizeOptions[0];
    }

    if (this.data.printSideOptions && this.data.printSideOptions.length > 0) {
      settings.printSide = this.data.printSideOptions[0];
    }

    if (this.data.directionOptions && this.data.directionOptions.length > 0) {
      settings.direction = this.data.directionOptions[0];
    }

    if (this.data.colorTypeOptions && this.data.colorTypeOptions.length > 0) {
      settings.colorType = this.data.colorTypeOptions[0];
    }

    if (this.data.paperTypeOptions && this.data.paperTypeOptions.length > 0) {
      settings.paperType = this.data.paperTypeOptions[0];
    }

    if (this.data.bindingOptions && this.data.bindingOptions.length > 0) {
      settings.binding = this.data.bindingOptions[0];
    }

    if (this.data.multiPageOptions && this.data.multiPageOptions.length > 0) {
      settings.multiPage = this.data.multiPageOptions[0];
    }

    console.log('生成的默认设置:', settings);
    return settings;
  },

  // 格式化打印参数显示
  formatPrintParams: function(settings) {
    const params = [];

    if (settings.paperSize) params.push(settings.paperSize);
    if (settings.colorType) params.push(settings.colorType);
    if (settings.printSide) params.push(settings.printSide);
    if (settings.direction) params.push(settings.direction);
    if (settings.paperType) params.push(settings.paperType);
    if (settings.binding && settings.binding !== '不装订') params.push(settings.binding);
    if (settings.multiPage && settings.multiPage !== '不缩印') params.push(settings.multiPage);

    return params.join(' | ');
  },

  // 获取文件图标
  getFileIcon: function(fileName) {
    if (!fileName) return '/images/icon/file_default.png';

    const ext = fileName.split('.').pop().toLowerCase();
    const iconMap = {
      'pdf': '/images/icon/file_pdf.png',
      'doc': '/images/icon/file_word.png',
      'docx': '/images/icon/file_word.png',
      'xls': '/images/icon/file_excel.png',
      'xlsx': '/images/icon/file_excel.png',
      'ppt': '/images/icon/file_ppt.png',
      'pptx': '/images/icon/file_ppt.png',
      'txt': '/images/icon/file_txt.png',
      'jpg': '/images/icon/file_image.png',
      'jpeg': '/images/icon/file_image.png',
      'png': '/images/icon/file_image.png',
      'gif': '/images/icon/file_image.png'
    };

    return iconMap[ext] || '/images/icon/file_default.png';
  },

  // 切换快速设置显示状态
  toggleQuickSettings: function(e) {
    const id = e.currentTarget.dataset.id;
    const printList = this.data.printList.map(item => {
      if (item.id == id) {
        return {
          ...item,
          showQuickSettings: !item.showQuickSettings
        };
      }
      return item;
    });

    this.setData({
      printList: printList
    });
  },

  // 更新快速设置
  updateQuickSetting: function(e) {
    const { id, field, value } = e.currentTarget.dataset;
    const printList = this.data.printList.map(item => {
      if (item.id == id) {
        const newPrintSettings = {
          ...item.printSettings,
          [field]: value
        };

        return {
          ...item,
          printSettings: newPrintSettings,
          fileParams: this.formatPrintParams(newPrintSettings)
        };
      }
      return item;
    });

    this.setData({
      printList: printList
    });

    // 重新计算价格
    this.calculateTotal();
  },

  // 选择/取消选择所有文件
  toggleSelectAll: function() {
    const printList = this.data.printList;
    const newSelectState = !this.isAllSelected();
    
    const newList = printList.map(item => {
      return {
        ...item,
        selected: newSelectState
      };
    });
    
    this.setData({
      printList: newList
    });
    
    this.calculateTotal();
  },
  
  // 检查是否已全选
  isAllSelected: function() {
    return this.data.printList.every(item => item.selected);
  },
  
  // 选择/取消选择单个文件
  toggleSelect: function(e) {
    const id = e.currentTarget.dataset.id;
    const printList = this.data.printList;
    
    const newList = printList.map(item => {
      if (item.id === id) {
        return {
          ...item,
          selected: !item.selected
        };
      }
      return item;
    });
    
    this.setData({
      printList: newList
    });
    
    this.calculateTotal();
  },
  
  // 计算总价和选中数量
  calculateTotal: function() {
    const printList = this.data.printList;
    let totalPrice = 0;
    let selectedCount = 0;
    
    printList.forEach(item => {
      if (item.selected) {
        totalPrice += item.price;
        selectedCount++;
      }
    });
    
    this.setData({
      totalPrice: totalPrice.toFixed(2),
      selectedCount: selectedCount
    });
  },
  
  // 查看/编辑打印设置
  editPrintSettings: function(e) {
    const id = e.currentTarget.dataset.id;
    // 获取文件数据
    const file = this.data.printList.find(item => item.id === id);
    
    if (file) {
      wx.navigateTo({
        url: '/pages/print/settings/settings?id=' + id
      });
    }
  },
  
  // 预览文件
  previewFile: function(e) {
    var path = e.currentTarget.dataset.path
    sender.requestUrl({
      url: api.api_print_preview,
      method: 'GET',
      params: {
        filePath: path
      }
    }, function(res) {
        console.log("预览接口",res)
    });
  },
  
  // 删除文件
  deleteFile: function(e) {
    const id = e.currentTarget.dataset.id;
    const printList = this.data.printList;
    
    const newList = printList.filter(item => item.id !== id);
    
    this.setData({
      printList: newList
    });
    
    this.calculateTotal();
  },
  
  // 继续上传文件
  continueUpload: function() {
    wx.navigateTo({
      url: '/pages/print/upload/upload'
    });
  },

  // 结算
  checkout: function() {
    if (this.data.selectedCount === 0) {
      wx.showToast({
        title: '请先选择要打印的文件',
        icon: 'none'
      });
      return;
    }
    var that = this
    console.log(that.data.printList)
    sender.requestUrl({
      url: api.api_order_pre_order,
      method: 'POST',
      data: {
        printFileList: that.data.printList
      }
    },(res)=>{
      console.log(res)
        if(res.data.code == 0){

        }
    })

    setTimeout(() => {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }, 1500);
  }
}) 