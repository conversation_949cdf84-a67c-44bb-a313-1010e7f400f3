var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();

Page({
  data: {
    printList: [],
    totalPrice: 0.68,
    selectedCount: 1,
    functions: [
      {
        name: '批量设置',
        icon: '/images/icon/icon_batch_set.png'
      },
      {
        name: '批量删除',
        icon: '/images/icon/icon_delete.png'
      }
    ],
    deliveryFree: true,
    freeShippingThreshold: 3.9,
    freeExpressThreshold: 9.9
  },

  onLoad: function(options) {
    console.log(options)
    let data = {
      fileName: options.fileName,
      filePath: options.filePath,
      quantity: 1
    }
    this.setData({
      printList:[data]
    })
  },
  
  // 选择/取消选择所有文件
  toggleSelectAll: function() {
    const printList = this.data.printList;
    const newSelectState = !this.isAllSelected();
    
    const newList = printList.map(item => {
      return {
        ...item,
        selected: newSelectState
      };
    });
    
    this.setData({
      printList: newList
    });
    
    this.calculateTotal();
  },
  
  // 检查是否已全选
  isAllSelected: function() {
    return this.data.printList.every(item => item.selected);
  },
  
  // 选择/取消选择单个文件
  toggleSelect: function(e) {
    const id = e.currentTarget.dataset.id;
    const printList = this.data.printList;
    
    const newList = printList.map(item => {
      if (item.id === id) {
        return {
          ...item,
          selected: !item.selected
        };
      }
      return item;
    });
    
    this.setData({
      printList: newList
    });
    
    this.calculateTotal();
  },
  
  // 计算总价和选中数量
  calculateTotal: function() {
    const printList = this.data.printList;
    let totalPrice = 0;
    let selectedCount = 0;
    
    printList.forEach(item => {
      if (item.selected) {
        totalPrice += item.price;
        selectedCount++;
      }
    });
    
    this.setData({
      totalPrice: totalPrice.toFixed(2),
      selectedCount: selectedCount
    });
  },
  
  // 查看/编辑打印设置
  editPrintSettings: function(e) {
    const id = e.currentTarget.dataset.id;
    // 获取文件数据
    const file = this.data.printList.find(item => item.id === id);
    
    if (file) {
      wx.navigateTo({
        url: '/pages/print/settings/settings?id=' + id
      });
    }
  },
  
  // 预览文件
  previewFile: function(e) {
    var path = e.currentTarget.dataset.path
    sender.requestUrl({
      url: api.api_print_preview,
      method: 'GET',
      params: {
        filePath: path
      }
    }, function(res) {
        console.log("预览接口",res)
    });
  },
  
  // 删除文件
  deleteFile: function(e) {
    const id = e.currentTarget.dataset.id;
    const printList = this.data.printList;
    
    const newList = printList.filter(item => item.id !== id);
    
    this.setData({
      printList: newList
    });
    
    this.calculateTotal();
  },
  
  // 结算
  checkout: function() {
    if (this.data.selectedCount === 0) {
      wx.showToast({
        title: '请先选择要打印的文件',
        icon: 'none'
      });
      return;
    }
    var that = this
    console.log(that.data.printList)
    sender.requestUrl({
      url: api.api_order_pre_order,
      method: 'POST',
      data: {
        printFileList: that.data.printList
      }
    },(res)=>{
      console.log(res)
        if(res.data.code == 0){

        }
    })
    
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }, 1500);
  }
}) 