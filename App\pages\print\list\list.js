var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();

Page({
  data: {
    printList: [{
      "fileName": "测试文件.PDF",
      "type": "PDF",
      "pageNum": 1,
      "pageSize": 10,
      "quantity": 1,
      "unit_price": 1.68,
      "skuId": 1,
      "specs": "A4/喷墨/双面",
      "selected": false
    }, {
      "fileName": "工作报告.DOCX",
      "type": "DOCX",
      "pageNum": 1,
      "pageSize": 5,
      "quantity": 2,
      "unit_price": 2.50,
      "skuId": 2,
      "specs": "A4/激光/单面",
      "selected": true
    }, {
      "fileName": "数据表格.XLSX",
      "type": "XLSX",
      "pageNum": 2,
      "pageSize": 3,
      "quantity": 1,
      "unit_price": 1.20,
      "skuId": 3,
      "specs": "A4/喷墨/双面",
      "selected": false
    }],
    totalPrice: 2.50,
    selectedCount: 1,
    isAllSelected: false
  },

  onLoad: function(options) {
    // 页面加载时计算总价
    this.calculateTotal();
  },

  // 判断是否全选
  isAllSelected: function() {
    const printList = this.data.printList;
    if (printList.length === 0) return false;
    return printList.every(item => item.selected);
  },

  // 计算总价和选中数量
  calculateTotal: function() {
    const printList = this.data.printList;
    let totalPrice = 0;
    let selectedCount = 0;

    printList.forEach(item => {
      if (item.selected) {
        totalPrice += item.unit_price * item.quantity;
        selectedCount++;
      }
    });

    const isAllSelected = printList.length > 0 && printList.every(item => item.selected);

    this.setData({
      totalPrice: totalPrice.toFixed(2),
      selectedCount: selectedCount,
      isAllSelected: isAllSelected
    });
  },
  
  // 查看/编辑打印设置
  editPrintSettings: function(e) {
    const id = e.currentTarget.dataset.id;
    // 获取文件数据
    const file = this.data.printList.find(item => item.id === id);
    
    if (file) {
      wx.navigateTo({
        url: '/pages/print/settings/settings?id=' + id
      });
    }
  },

  
  // 删除文件
  deleteFile: function(e) {
    const id = e.currentTarget.dataset.id;
    const printList = this.data.printList;
    
    const newList = printList.filter(item => item.id !== id);
    
    this.setData({
      printList: newList
    });
    
    this.calculateTotal();
  },
  
  // 继续上传文件
  continueUpload: function() {
    wx.navigateTo({
      url: '/pages/print/upload/upload'
    });
  },

  // 结算
  checkout: function() {
    if (this.data.selectedCount === 0) {
      wx.showToast({
        title: '请先选择要打印的文件',
        icon: 'none'
      });
      return;
    }
    var that = this
    console.log(that.data.printList)
    sender.requestUrl({
      url: api.api_order_pre_order,
      method: 'POST',
      data: {
        printFileList: that.data.printList
      }
    },(res)=>{
      console.log(res)
        if(res.data.code == 0){

        }
    })

    setTimeout(() => {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }, 1500);
  }
}) 