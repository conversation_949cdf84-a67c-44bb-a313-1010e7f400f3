var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();

Page({
  data: {
    printList: [],
    totalPrice: 0.68,
    selectedCount: 1,
    functions: [{
      "fileName": "测试文件.PDF",
      "type": "PDF",
      "pageNum": 1,
      "pageSize": 10,
      "quantity": 1,
      "unit_price":1.68,
      "skuId":1,
      "specs":"A4/喷墨/双面"
  }]
  },

  onLoad: function(options) {
   
  },

  // 获取文件图标
  getFileIcon: function(fileName) {
    if (!fileName) return '/images/icon/file_default.png';

    const ext = fileName.split('.').pop().toLowerCase();
    const iconMap = {
      'pdf': '/images/icon/file_pdf.png',
      'doc': '/images/icon/file_word.png',
      'docx': '/images/icon/file_word.png',
      'xls': '/images/icon/file_excel.png',
      'xlsx': '/images/icon/file_excel.png',
      'ppt': '/images/icon/file_ppt.png',
      'pptx': '/images/icon/file_ppt.png',
      'txt': '/images/icon/file_txt.png',
      'jpg': '/images/icon/file_image.png',
      'jpeg': '/images/icon/file_image.png',
      'png': '/images/icon/file_image.png',
      'gif': '/images/icon/file_image.png'
    };

    return iconMap[ext] || '/images/icon/file_default.png';
  },

  // 计算总价和选中数量
  calculateTotal: function() {
    const printList = this.data.printList;
    let totalPrice = 0;
    let selectedCount = 0;
    
    printList.forEach(item => {
      if (item.selected) {
        totalPrice += item.price;
        selectedCount++;
      }
    });
    
    this.setData({
      totalPrice: totalPrice.toFixed(2),
      selectedCount: selectedCount
    });
  },
  
  // 查看/编辑打印设置
  editPrintSettings: function(e) {
    const id = e.currentTarget.dataset.id;
    // 获取文件数据
    const file = this.data.printList.find(item => item.id === id);
    
    if (file) {
      wx.navigateTo({
        url: '/pages/print/settings/settings?id=' + id
      });
    }
  },

  
  // 删除文件
  deleteFile: function(e) {
    const id = e.currentTarget.dataset.id;
    const printList = this.data.printList;
    
    const newList = printList.filter(item => item.id !== id);
    
    this.setData({
      printList: newList
    });
    
    this.calculateTotal();
  },
  
  // 继续上传文件
  continueUpload: function() {
    wx.navigateTo({
      url: '/pages/print/upload/upload'
    });
  },

  // 结算
  checkout: function() {
    if (this.data.selectedCount === 0) {
      wx.showToast({
        title: '请先选择要打印的文件',
        icon: 'none'
      });
      return;
    }
    var that = this
    console.log(that.data.printList)
    sender.requestUrl({
      url: api.api_order_pre_order,
      method: 'POST',
      data: {
        printFileList: that.data.printList
      }
    },(res)=>{
      console.log(res)
        if(res.data.code == 0){

        }
    })

    setTimeout(() => {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }, 1500);
  }
}) 