<view class="container">
  <custom-nav title="打印列表" color="#333" showBack="{{true}}"></custom-nav>

  <!-- 打印文件列表 -->
  <view class="print-list">
    <block wx:if="{{printList.length > 0}}">
      <view class="print-item" wx:for="{{printList}}" wx:key="skuId">
        <!-- 文件信息区域 -->
        <view class="file-header">
          <view class="select-box" bindtap="toggleSelect" data-index="{{index}}">
            <view class="{{item.selected ? 'select-circle selected' : 'select-circle'}}">
              <view class="select-inner" wx:if="{{item.selected}}"></view>
            </view>
          </view>

          <!-- 根据文件类型显示对应图标 -->
          <view class="file-icon-wrapper">
            <image wx:if="{{item.type === 'PDF'}}" class="file-icon" src="/images/icon/pdf_icon.png" mode="aspectFit"></image>
            <image wx:elif="{{item.type === 'DOC' || item.type === 'DOCX'}}" class="file-icon" src="/images/icon/word_icon.png" mode="aspectFit"></image>
            <image wx:elif="{{item.type === 'XLS' || item.type === 'XLSX'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
            <image wx:elif="{{item.type === 'PPT' || item.type === 'PPTX'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
            <image wx:elif="{{item.type === 'TXT'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
            <image wx:elif="{{item.type === 'JPG' || item.type === 'JPEG' || item.type === 'PNG' || item.type === 'GIF'}}" class="file-icon" src="/images/icon/image_icon.png" mode="aspectFit"></image>
            <image wx:else class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
          </view>

          <view class="file-info">
            <view class="file-name">{{item.fileName}}</view>
            <view class="file-specs">{{item.specs}}/第{{item.pageNum}}-{{item.pageNum + item.pageSize - 1}}页</view>
          </view>

          <view class="delete-btn" bindtap="deleteFile" data-index="{{index}}">×</view>
        </view>

        <!-- 价格、数量和操作区域 -->
        <view class="file-footer">
          <view class="price-section">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{item.unit_price}}</text>
            <text class="price-unit">元</text>
          </view>

          <view class="quantity-controls">
            <view class="quantity-btn minus" bindtap="decreaseQuantity" data-index="{{index}}">-</view>
            <input class="quantity-input" type="number" value="{{item.quantity}}" bindinput="onQuantityInput" data-index="{{index}}" />
            <view class="quantity-btn plus" bindtap="increaseQuantity" data-index="{{index}}">+</view>
            <text class="quantity-unit">份</text>
          </view>

          <view class="settings-btn" bindtap="openSettings" data-id="{{item.skuId}}">打印设置</view>
        </view>
      </view>
    </block>

    <!-- 空状态提示 -->
    <view wx:else class="empty-tip">
      <image class="empty-icon" src="/images/icon/icon_upload.png" mode="aspectFit"></image>
      <view class="empty-text">暂无打印文件</view>
      <view class="empty-desc">请先上传需要打印的文件</view>
    </view>
  </view>

  <!-- 提交打印前请先预览提示 -->
  <view class="preview-tip">
    提交打印前请先预览文件内容、检查打印设置<br/>
    若预览与您电脑手机查看不一致，以预览为准<br/>
    如担心文件版式变化，可将文件转成PDF上传<br/>
    工厂实行自动化打印，无人工检查或处理文件
  </view>

  <!-- 底部空白占位，确保内容不被底部工具栏遮挡 -->
  <view class="bottom-placeholder"></view>

  <!-- 悬浮添加按钮 -->
  <view class="floating-add-btn">
    <text class="add-icon">+</text>
  </view>

  <!-- 底部固定操作区 -->
  <view class="bottom-fixed-area">
    <!-- 操作功能区 -->
    <view class="function-tabs">
      <view class="tab-item">批量设置</view>
      <view class="tab-item">批量删除</view>
    </view>

    <!-- 结算栏 -->
    <view class="checkout-bar">
      <!-- 左侧选择区 -->
      <view class="left-selection">
        <view class="select-circle {{isAllSelected ? 'selected' : ''}}">
          <view class="select-inner" wx:if="{{isAllSelected}}"></view>
        </view>
        <text class="select-text">全选</text>
      </view>

      <!-- 右侧结算区 -->
      <view class="right-checkout">
        <view class="order-summary">
          <text>已选{{selectedCount}}件，合计：</text>
          <text class="price-symbol">¥</text>
          <text class="price-value">{{totalPrice}}</text>
        </view>
        <view class="checkout-btn">结算</view>
      </view>
    </view>
  </view>
</view>