<view class="container">
  <custom-nav title="打印列表" color="#333" showBack="{{true}}"></custom-nav>
  
  <!-- 打印文件列表 -->
  <view class="print-list">
    <block wx:if="{{printList.length > 0}}">
      <view class="print-item" wx:for="{{printList}}" wx:key="id">
        <view class="select-box" bindtap="toggleSelect" data-id="{{item.id}}">
          <view class="{{item.selected ? 'select-circle selected' : 'select-circle'}}">
            <view class="select-inner" wx:if="{{item.selected}}"></view>
          </view>
        </view>
        
        <view class="file-info">
          <view class="file-top">
            <image class="file-icon" src="{{item.fileIcon}}" mode="aspectFit"></image>
            <view class="file-name">{{item.fileName}}</view>
            <view class="delete-btn" bindtap="deleteFile" data-id="{{item.id}}">×</view>
          </view>
          
          <view class="file-params">
            {{item.fileParams}}
          </view>
          
          <view class="file-actions">
            <view class="file-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{item.price}}</text>
              <text class="price-unit">元</text>
            </view>
            
            <view class="action-btns">
              <view class="preview-btn" bindtap="previewFile" data-path="{{item.filePath}}">预览</view>
              <view class="settings-btn" bindtap="editPrintSettings" data-id="{{item.id}}">打印设置</view>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <view class="empty-tip" wx:if="{{printList.length === 0}}">
      暂无打印文件，请先上传文件
    </view>
  </view>
  
  <!-- 提交打印前请先预览提示 -->
  <view class="preview-tip">
    提交打印前请先预览文件内容、检查打印设置<br/>
    若预览与您电脑手机查看不一致，以预览为准<br/>
    如担心文件版式变化，可将文件转成PDF上传<br/>
    工厂实行自动化打印，无人工检查或处理文件
  </view>
  
  <!-- 底部空白占位，确保内容不被底部工具栏遮挡 -->
  <view class="bottom-placeholder"></view>
  
  
  <!-- 悬浮添加按钮 -->
  <view class="floating-add-btn" bindtap="continueUpload">
    <text class="add-icon">+</text>
  </view>

  <!-- 底部固定操作区 -->
  <view class="bottom-fixed-area">
    <!-- 操作功能区 -->
    <view class="function-tabs">
      <view class="tab-item">批量设置</view>
      <view class="tab-item">批量删除</view>
    </view>

    <!-- 结算栏 -->
    <view class="checkout-bar">
      <!-- 左侧选择区 -->
      <view class="left-selection">
        <view class="select-circle {{isAllSelected() ? 'selected' : ''}}" bindtap="toggleSelectAll">
          <view class="select-inner" wx:if="{{isAllSelected()}}"></view>
        </view>
        <text class="select-text">全选</text>
      </view>

      <!-- 右侧结算区 -->
      <view class="right-checkout">
        <view class="order-summary">
          <text>已选{{selectedCount}}件，合计：</text>
          <text class="price-symbol">¥</text>
          <text class="price-value">{{totalPrice}}</text>
        </view>
        <view class="checkout-btn" bindtap="checkout">结算</view>
      </view>
    </view>
  </view>
</view> 